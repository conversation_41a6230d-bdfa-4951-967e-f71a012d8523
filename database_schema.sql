-- 在线博彩游戏平台数据库结构
-- 适用于 PostgreSQL + dbdiagram.io

-- 用户等级表
CREATE TABLE user_levels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL COMMENT '等级名称',
    level_value INTEGER NOT NULL UNIQUE COMMENT '等级数值',
    min_deposit DECIMAL(15,2) DEFAULT 0 COMMENT '最低充值要求',
    max_withdrawal DECIMAL(15,2) COMMENT '最大提现限额',
    commission_rate DECIMAL(5,4) DEFAULT 0 COMMENT '佣金比例',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户信息表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) UNIQUE COMMENT '邮箱',
    phone VARCHAR(20) UNIQUE COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(100) COMMENT '真实姓名',
    id_card VARCHAR(50) COMMENT '身份证号',
    birth_date DATE COMMENT '出生日期',
    gender SMALLINT COMMENT '性别 1:男 2:女',
    level_id INTEGER REFERENCES user_levels(id) COMMENT '用户等级',
    agent_id UUID REFERENCES users(id) COMMENT '代理ID',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:正常 2:冻结 3:禁用',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否实名认证',
    last_login_at TIMESTAMP WITH TIME ZONE COMMENT '最后登录时间',
    last_login_ip INET COMMENT '最后登录IP',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户钱包表
CREATE TABLE user_wallets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    balance DECIMAL(15,2) DEFAULT 0 COMMENT '可用余额',
    frozen_balance DECIMAL(15,2) DEFAULT 0 COMMENT '冻结余额',
    total_deposit DECIMAL(15,2) DEFAULT 0 COMMENT '累计充值',
    total_withdrawal DECIMAL(15,2) DEFAULT 0 COMMENT '累计提现',
    total_bet DECIMAL(15,2) DEFAULT 0 COMMENT '累计投注',
    total_win DECIMAL(15,2) DEFAULT 0 COMMENT '累计赢取',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id)
);

-- 游戏分类表
CREATE TABLE game_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '分类代码',
    icon VARCHAR(255) COMMENT '图标URL',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 游戏列表
CREATE TABLE games (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    category_id INTEGER NOT NULL REFERENCES game_categories(id),
    name VARCHAR(200) NOT NULL COMMENT '游戏名称',
    code VARCHAR(100) NOT NULL UNIQUE COMMENT '游戏代码',
    provider VARCHAR(100) COMMENT '游戏提供商',
    game_type VARCHAR(50) COMMENT '游戏类型',
    min_bet DECIMAL(10,2) DEFAULT 0 COMMENT '最小投注',
    max_bet DECIMAL(10,2) COMMENT '最大投注',
    rtp DECIMAL(5,4) COMMENT '返还率',
    thumbnail VARCHAR(255) COMMENT '缩略图',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 游戏记录表
CREATE TABLE game_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    game_id UUID NOT NULL REFERENCES games(id),
    bet_amount DECIMAL(15,2) NOT NULL COMMENT '投注金额',
    win_amount DECIMAL(15,2) DEFAULT 0 COMMENT '赢取金额',
    profit_loss DECIMAL(15,2) NOT NULL COMMENT '盈亏金额',
    game_round_id VARCHAR(100) COMMENT '游戏局号',
    game_data JSONB COMMENT '游戏详细数据',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:正常 2:取消',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 支付方式表
CREATE TABLE payment_methods (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '支付方式名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '支付代码',
    type SMALLINT NOT NULL COMMENT '类型 1:充值 2:提现 3:both',
    min_amount DECIMAL(10,2) DEFAULT 0 COMMENT '最小金额',
    max_amount DECIMAL(10,2) COMMENT '最大金额',
    fee_rate DECIMAL(5,4) DEFAULT 0 COMMENT '手续费率',
    fee_fixed DECIMAL(10,2) DEFAULT 0 COMMENT '固定手续费',
    status SMALLINT DEFAULT 1 COMMENT '状态',
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户银行卡表
CREATE TABLE user_bank_cards (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    bank_name VARCHAR(100) NOT NULL COMMENT '银行名称',
    card_number VARCHAR(50) NOT NULL COMMENT '卡号',
    card_holder VARCHAR(100) NOT NULL COMMENT '持卡人',
    is_default BOOLEAN DEFAULT FALSE COMMENT '是否默认',
    status SMALLINT DEFAULT 1 COMMENT '状态',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 充值记录表
CREATE TABLE deposits (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    payment_method_id INTEGER NOT NULL REFERENCES payment_methods(id),
    order_no VARCHAR(100) NOT NULL UNIQUE COMMENT '订单号',
    amount DECIMAL(15,2) NOT NULL COMMENT '充值金额',
    fee DECIMAL(15,2) DEFAULT 0 COMMENT '手续费',
    actual_amount DECIMAL(15,2) NOT NULL COMMENT '实际到账金额',
    status SMALLINT DEFAULT 0 COMMENT '状态 0:待处理 1:成功 2:失败 3:取消',
    payment_data JSONB COMMENT '支付相关数据',
    remark TEXT COMMENT '备注',
    processed_at TIMESTAMP WITH TIME ZONE COMMENT '处理时间',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 提现记录表
CREATE TABLE withdrawals (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    bank_card_id UUID NOT NULL REFERENCES user_bank_cards(id),
    order_no VARCHAR(100) NOT NULL UNIQUE COMMENT '订单号',
    amount DECIMAL(15,2) NOT NULL COMMENT '提现金额',
    fee DECIMAL(15,2) DEFAULT 0 COMMENT '手续费',
    actual_amount DECIMAL(15,2) NOT NULL COMMENT '实际到账金额',
    status SMALLINT DEFAULT 0 COMMENT '状态 0:待审核 1:处理中 2:成功 3:失败 4:取消',
    admin_remark TEXT COMMENT '管理员备注',
    processed_at TIMESTAMP WITH TIME ZONE COMMENT '处理时间',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 佣金记录表
CREATE TABLE commissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) COMMENT '获得佣金的用户',
    from_user_id UUID NOT NULL REFERENCES users(id) COMMENT '产生佣金的用户',
    type SMALLINT NOT NULL COMMENT '佣金类型 1:投注佣金 2:充值佣金 3:推荐佣金',
    amount DECIMAL(15,2) NOT NULL COMMENT '佣金金额',
    rate DECIMAL(5,4) NOT NULL COMMENT '佣金比例',
    source_amount DECIMAL(15,2) NOT NULL COMMENT '源金额',
    source_id UUID COMMENT '来源记录ID',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:正常 2:取消',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 积分记录表
CREATE TABLE points_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    type SMALLINT NOT NULL COMMENT '积分类型 1:获得 2:消费',
    points INTEGER NOT NULL COMMENT '积分数量',
    balance_before INTEGER NOT NULL COMMENT '变动前余额',
    balance_after INTEGER NOT NULL COMMENT '变动后余额',
    source_type VARCHAR(50) COMMENT '来源类型',
    source_id UUID COMMENT '来源ID',
    remark VARCHAR(255) COMMENT '备注',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 活动列表
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(200) NOT NULL COMMENT '活动标题',
    description TEXT COMMENT '活动描述',
    type SMALLINT NOT NULL COMMENT '活动类型 1:充值奖励 2:投注返利 3:签到奖励',
    conditions JSONB COMMENT '参与条件',
    rewards JSONB COMMENT '奖励设置',
    start_time TIMESTAMP WITH TIME ZONE NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP WITH TIME ZONE NOT NULL COMMENT '结束时间',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:启用 0:禁用',
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 活动记录表
CREATE TABLE promotion_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    promotion_id UUID NOT NULL REFERENCES promotions(id),
    reward_amount DECIMAL(15,2) COMMENT '奖励金额',
    reward_points INTEGER COMMENT '奖励积分',
    status SMALLINT DEFAULT 1 COMMENT '状态 1:已发放 2:已取消',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 返水记录表
CREATE TABLE rebate_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id),
    period_start DATE NOT NULL COMMENT '返水周期开始',
    period_end DATE NOT NULL COMMENT '返水周期结束',
    total_bet DECIMAL(15,2) NOT NULL COMMENT '总投注额',
    rebate_rate DECIMAL(5,4) NOT NULL COMMENT '返水比例',
    rebate_amount DECIMAL(15,2) NOT NULL COMMENT '返水金额',
    status SMALLINT DEFAULT 0 COMMENT '状态 0:待发放 1:已发放',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE system_configs (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    type VARCHAR(50) DEFAULT 'string' COMMENT '数据类型',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) COMMENT '操作用户',
    action VARCHAR(100) NOT NULL COMMENT '操作动作',
    resource_type VARCHAR(50) COMMENT '资源类型',
    resource_id VARCHAR(100) COMMENT '资源ID',
    old_data JSONB COMMENT '变更前数据',
    new_data JSONB COMMENT '变更后数据',
    ip_address INET COMMENT 'IP地址',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 消息通知表
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) COMMENT '接收用户',
    type SMALLINT NOT NULL COMMENT '通知类型 1:系统 2:活动 3:交易',
    title VARCHAR(200) NOT NULL COMMENT '标题',
    content TEXT COMMENT '内容',
    is_read BOOLEAN DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- IP管理表
CREATE TABLE ip_controls (
    id SERIAL PRIMARY KEY,
    ip_address INET NOT NULL COMMENT 'IP地址',
    type SMALLINT NOT NULL COMMENT '类型 1:白名单 2:黑名单',
    reason VARCHAR(255) COMMENT '原因',
    status SMALLINT DEFAULT 1 COMMENT '状态',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引创建
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_users_agent_id ON users(agent_id);
CREATE INDEX idx_users_status ON users(status);

CREATE INDEX idx_game_records_user_id ON game_records(user_id);
CREATE INDEX idx_game_records_game_id ON game_records(game_id);
CREATE INDEX idx_game_records_created_at ON game_records(created_at);

CREATE INDEX idx_deposits_user_id ON deposits(user_id);
CREATE INDEX idx_deposits_status ON deposits(status);
CREATE INDEX idx_deposits_created_at ON deposits(created_at);

CREATE INDEX idx_withdrawals_user_id ON withdrawals(user_id);
CREATE INDEX idx_withdrawals_status ON withdrawals(status);
CREATE INDEX idx_withdrawals_created_at ON withdrawals(created_at);

CREATE INDEX idx_commissions_user_id ON commissions(user_id);
CREATE INDEX idx_commissions_from_user_id ON commissions(from_user_id);

CREATE INDEX idx_points_records_user_id ON points_records(user_id);
CREATE INDEX idx_promotion_records_user_id ON promotion_records(user_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
